<template>
  <div class="metaso-config q-gutter-y-md">
    <q-input
      :model-value="config.baseUrl"
      :label="$t('src.components.settings.providers.MetasoProviderConfig.baseUrlLabel')"
      outlined
      dense
      :placeholder="$t('src.components.settings.providers.MetasoProviderConfig.baseUrlPlaceholder')"
      :hint="$t('src.components.settings.providers.MetasoProviderConfig.baseUrlHint')"
      @update:model-value="updateConfig('baseUrl', $event)"
    />

    <q-input
      :model-value="config.apiKey"
      :label="$t('src.components.settings.providers.MetasoProviderConfig.apiKeyLabel')"
      outlined
      dense
      type="password"
      :placeholder="$t('src.components.settings.providers.MetasoProviderConfig.apiKeyPlaceholder')"
      :hint="$t('src.components.settings.providers.MetasoProviderConfig.apiKeyHint')"
      @update:model-value="updateConfig('apiKey', $event)"
    />

    <q-input
      :model-value="config.size"
      :label="$t('src.components.settings.providers.MetasoProviderConfig.sizeLabel')"
      outlined
      dense
      type="number"
      :placeholder="$t('src.components.settings.providers.MetasoProviderConfig.sizePlaceholder')"
      :hint="$t('src.components.settings.providers.MetasoProviderConfig.sizeHint')"
      @update:model-value="updateConfig('size', parseInt(String($event)) || 10)"
    />

    <div class="text-subtitle2 q-mt-md q-mb-sm">
      {{ $t('src.components.settings.providers.MetasoProviderConfig.searchOptions') }}
    </div>

    <q-toggle
      :model-value="config.includeSummary"
      :label="$t('src.components.settings.providers.MetasoProviderConfig.includeSummaryLabel')"
      @update:model-value="updateConfig('includeSummary', $event)"
    />
    <div class="text-caption text-grey-6 q-ml-md">
      {{ $t('src.components.settings.providers.MetasoProviderConfig.includeSummaryHint') }}
    </div>

    <q-toggle
      :model-value="config.includeRowContent"
      :label="$t('src.components.settings.providers.MetasoProviderConfig.includeRowContentLabel')"
      @update:model-value="updateConfig('includeRowContent', $event)"
    />
    <div class="text-caption text-grey-6 q-ml-md">
      {{ $t('src.components.settings.providers.MetasoProviderConfig.includeRowContentHint') }}
    </div>
  </div>
</template>

<script setup lang="ts">
import type { MetasoSettings } from 'src/env.d';
import { useI18n } from 'vue-i18n';

const { t: $t } = useI18n({ useScope: 'global' });

interface Props {
  config: MetasoSettings;
}

interface Emits {
  (e: 'update', config: MetasoSettings): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 更新配置
const updateConfig = (key: keyof MetasoSettings, value: string | number | boolean) => {
  const updatedConfig = {
    ...props.config,
    [key]: value,
  };
  emit('update', updatedConfig);
};
</script>

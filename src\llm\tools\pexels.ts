import { useUiStore } from 'src/stores/ui';
import type { PexelsVideo } from 'src/env.d';
import { $t } from 'src/composables/useTrans';

interface PexelsPhotoSource {
  original: string;
  large2x: string;
  large: string;
  medium: string;
  small: string;
  portrait: string;
  landscape: string;
  tiny: string;
}

export interface PexelsPhoto {
  id: number;
  width: number;
  height: number;
  url: string;
  photographer: string;
  photographer_url: string;
  photographer_id: number;
  avg_color: string;
  src: PexelsPhotoSource;
  liked: boolean;
  alt: string;
}

interface PexelsSearchResponse {
  photos: PexelsPhoto[];
  total_results: number;
  page: number;
  per_page: number;
  next_page?: string;
}

interface ReturnedPhotoSource {
  original: string;
  large: string;
  medium: string;
  small: string;
  tiny: string;
}

interface ReturnedPhoto {
  id: number;
  photographer: string;
  photographer_url: string;
  src: ReturnedPhotoSource;
}

interface PexelsSearchOptions {
  query: string;
}

interface PexelsVideoSearchResponse {
  videos: PexelsVideo[];
  total_results: number;
  page: number;
  per_page: number;
  next_page?: string;
}

interface ReturnedVideo {
  id: number;
  user: {
    id: number;
    name: string;
    url: string;
  };
  duration: number;
  image: string;
  video_files: Array<{
    id: number;
    quality: string;
    file_type: string;
    width: number;
    height: number;
    fps: number;
    link: string;
  }>;
}

async function searchPexels(options: PexelsSearchOptions): Promise<{
  success: boolean;
  message: string;
  tool_name: string;
  operation?: string;
  images?: ReturnedPhoto[];
}> {
  try {
    console.log('🖼️ [Pexels Search] 开始搜索:', options.query);

    const uiStore = useUiStore();

    const defaultProvider = uiStore.defaultResourceProvider;
    if (!defaultProvider || defaultProvider.key !== 'pexels') {
      return {
        success: false,
        message: $t('src.llm.tools.pexels.noPexelsResourceProvider'),
        tool_name: 'pexels_search',
        operation: 'search',
        images: [],
      };
    }

    const config = defaultProvider.config as {
      baseUrl: string;
      apiKey: string;
      maxResults: number;
    };
    const baseUrl = config.baseUrl;
    const apiKey = config.apiKey;
    const maxResults = config.maxResults || 6;
    console.log('🖼️ [Pexels Search] pexelsSettings:', defaultProvider.config);

    if (!baseUrl || !apiKey) {
      return {
        success: false,
        message: $t('src.llm.tools.pexels.pexelsApiCredentialsNotConfigured'),
        tool_name: 'search_pexels',
        operation: 'search',
      };
    }
    const response = await fetch(
      `${baseUrl}/v1/search?query=${options.query}&per_page=${maxResults}`,
      {
        headers: {
          Authorization: apiKey,
        },
      },
    );

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ [Pexels Search] API请求失败:', response.status, errorText);
      return {
        success: false,
        message: $t('src.llm.tools.pexels.pexelsApiRequestFailed', {
          status: response.status,
          statusText: response.statusText,
        }),
        tool_name: 'search_pexels',
        operation: 'search',
      };
    }

    const data: PexelsSearchResponse = await response.json();
    console.log('✅ [Pexels Search] 搜索成功，图片数:', data.photos?.length || 0);

    const images = data.photos.map((photo: PexelsPhoto) => ({
      id: photo.id,
      photographer: photo.photographer,
      photographer_url: photo.photographer_url,
      src: {
        original: photo.src.original,
        large: photo.src.large,
        medium: photo.src.medium,
        small: photo.src.small,
        tiny: photo.src.tiny,
      },
    }));

    return {
      success: true,
      message: $t('src.llm.tools.pexels.successfullyFetchedImages', {
        count: images.length,
        query: options.query,
      }),
      tool_name: 'search_pexels',
      operation: 'search',
      images,
    };
  } catch (error) {
    console.error('❌ [Pexels Search] 搜索失败:', error);
    return {
      success: false,
      message: $t('src.llm.tools.pexels.pexelsSearchFailed', {
        error: error instanceof Error ? error.message : String(error),
      }),
      tool_name: 'search_pexels',
      operation: 'search',
    };
  }
}

async function searchPexelsVideos(options: PexelsSearchOptions): Promise<{
  success: boolean;
  message: string;
  tool_name: string;
  operation?: string;
  videos?: ReturnedVideo[];
}> {
  try {
    console.log('🎬 [Pexels Video Search] 开始搜索:', options.query);

    const uiStore = useUiStore();

    const defaultProvider = uiStore.defaultResourceProvider;
    if (!defaultProvider || defaultProvider.key !== 'pexels') {
      return {
        success: false,
        message: $t('src.llm.tools.pexels.noPexelsResourceProvider'),
        tool_name: 'pexels_video_search',
        operation: 'search',
        videos: [],
      };
    }

    const config = defaultProvider.config as {
      baseUrl: string;
      apiKey: string;
      maxResults: number;
    };
    const baseUrl = config.baseUrl;
    const apiKey = config.apiKey;
    const maxResults = config.maxResults || 6;
    console.log('🎬 [Pexels Video Search] pexelsSettings:', defaultProvider.config);

    if (!baseUrl || !apiKey) {
      return {
        success: false,
        message: $t('src.llm.tools.pexels.pexelsApiCredentialsNotConfigured'),
        tool_name: 'search_pexels_videos',
        operation: 'search',
      };
    }

    const response = await fetch(
      `${baseUrl}/videos/search?query=${options.query}&per_page=${maxResults}`,
      {
        headers: {
          Authorization: apiKey,
        },
      },
    );

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ [Pexels Video Search] API请求失败:', response.status, errorText);
      return {
        success: false,
        message: $t('src.llm.tools.pexels.pexelsVideoApiRequestFailed', {
          status: response.status,
          statusText: response.statusText,
        }),
        tool_name: 'search_pexels_videos',
        operation: 'search',
      };
    }

    const data: PexelsVideoSearchResponse = await response.json();
    console.log('✅ [Pexels Video Search] 搜索成功，视频数:', data.videos?.length || 0);

    const videos = data.videos.map((video: PexelsVideo) => ({
      id: video.id,
      user: video.user,
      duration: video.duration,
      image: video.image,
      video_files: video.video_files.map((file) => ({
        id: file.id,
        quality: file.quality,
        file_type: file.file_type,
        width: file.width,
        height: file.height,
        fps: file.fps,
        link: file.link,
      })),
    }));

    return {
      success: true,
      message: $t('src.llm.tools.pexels.successfullyFetchedVideos', {
        count: videos.length,
        query: options.query,
      }),
      tool_name: 'search_pexels_videos',
      operation: 'search',
      videos,
    };
  } catch (error) {
    console.error('❌ [Pexels Video Search] 搜索失败:', error);
    return {
      success: false,
      message: $t('src.llm.tools.pexels.pexelsVideoSearchFailed', {
        error: error instanceof Error ? error.message : String(error),
      }),
      tool_name: 'search_pexels_videos',
      operation: 'search',
    };
  }
}

export const pexelsTools = {
  search_pexels: searchPexels,
  search_pexels_videos: searchPexelsVideos,
};

export const tools = [
  {
    type: 'function' as const,
    function: {
      name: 'search_pexels',
      description:
        'Searches for images on Pexels based on a search query. Use this tool to find relevant images.',
      parameters: {
        type: 'object',
        properties: {
          query: {
            type: 'string',
            description: 'The search query for Pexels API to find images.',
          },
        },
        required: ['query'],
      },
    },
  },
  {
    type: 'function' as const,
    function: {
      name: 'search_pexels_videos',
      description:
        'Searches for videos on Pexels based on a search query. Use this tool to find relevant videos.',
      parameters: {
        type: 'object',
        properties: {
          query: {
            type: 'string',
            description: 'The search query for Pexels API to find videos.',
          },
        },
        required: ['query'],
      },
    },
  },
];

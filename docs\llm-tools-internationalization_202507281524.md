# LLM工具国际化处理文档

**创建时间**: 2025年7月28日 15:24  
**处理范围**: src/llm/tools/ 目录下的工具方法文件

## 处理概述

本次国际化处理涉及以下文件：
- `src/llm/tools/user.ts`
- `src/llm/tools/editor.ts`
- `src/llm/tools/knowledgeBase.ts`
- `src/llm/tools/pexels.ts`
- `src/llm/tools/search.ts`

参考文件：`src/llm/tools/file.ts`（已完成国际化处理）

## 处理要求

1. **返回的message内容需要国际化**：使用 `$t()` 函数处理所有用户可见的消息
2. **每个方法需要返回operation字段**：表示当前工具的操作类型
3. **参考file.ts的实现模式**：保持一致的代码风格和结构

## 具体处理内容

### 1. user.ts
**状态**: 完全重构
**主要修改**:
- 添加 `$t` 函数导入
- 修改 `OperationResult` 接口，添加 `operation` 和 `message` 字段
- 重构 `askUser` 方法，添加参数验证、错误处理和国际化支持
- 所有返回语句添加 `operation: 'ask'` 字段

**国际化键值**:
- `src.llm.tools.user.askUser.emptyQuestion`
- `src.llm.tools.user.askUser.success`
- `src.llm.tools.user.askUser.failed`
- `src.llm.tools.user.askUser.error`

### 2. editor.ts
**状态**: 添加operation字段
**主要修改**:
- 已有 `$t` 导入和国际化处理
- 为所有返回 `OperationResult` 的方法添加 `operation` 字段
- 修改相关接口定义以支持 `operation` 字段

**处理的方法**:
- `searchAndReplace`: `operation: 'replace'`
- `searchAndInsert`: `operation: 'insert'`
- `searchAndDelete`: `operation: 'delete'`
- `getDocumentInfo`: `operation: 'get_info'`
- `hasPendingChanges`: `operation: 'check_changes'`
- `acceptAllChanges`: `operation: 'accept_changes'`
- `rejectAllChanges`: `operation: 'reject_changes'`
- `formatAndResetDocument`: `operation: 'format_document'`

### 3. knowledgeBase.ts
**状态**: 添加operation字段
**主要修改**:
- 已有 `$t` 导入和国际化处理
- 保持现有的 `tool_name` 字段
- 添加 `operation: 'search'` 字段到所有返回语句

**处理的方法**:
- `searchKnowledge`: 添加 `operation: 'search'`

### 4. pexels.ts
**状态**: 添加operation字段
**主要修改**:
- 已有 `$t` 导入和国际化处理
- 保持现有的 `tool_name` 字段
- 添加 `operation: 'search'` 字段到所有返回语句

**处理的方法**:
- `searchPexels`: 添加 `operation: 'search'`
- `searchPexelsVideos`: 添加 `operation: 'search'`

### 5. search.ts
**状态**: 添加operation字段
**主要修改**:
- 已有 `$t` 导入和国际化处理
- 保持现有的 `tool_name` 字段
- 添加 `operation: 'search'` 字段到所有返回语句

**处理的方法**:
- `searchWeb`: 添加 `operation: 'search'`

## 设计原则

1. **向后兼容性**: 保持现有的 `tool_name` 字段不变，同时添加 `operation` 字段
2. **一致性**: 所有工具方法都遵循相同的返回结构和国际化模式
3. **类型安全**: 更新相关的TypeScript接口定义以确保类型安全
4. **错误处理**: 为所有可能的错误情况提供国际化的错误消息

## 操作类型定义

| 操作类型 | 描述 | 使用场景 |
|---------|------|----------|
| `ask` | 询问用户 | 用户交互工具 |
| `search` | 搜索操作 | 知识库搜索、网络搜索、图片搜索等 |
| `replace` | 替换文本 | 编辑器文本替换 |
| `insert` | 插入文本 | 编辑器文本插入 |
| `delete` | 删除文本 | 编辑器文本删除 |
| `get_info` | 获取信息 | 获取文档信息 |
| `check_changes` | 检查修改 | 检查待处理的修改 |
| `accept_changes` | 接受修改 | 接受所有修改 |
| `reject_changes` | 拒绝修改 | 拒绝所有修改 |
| `format_document` | 格式化文档 | 文档格式化和重设 |

## 验证结果

所有修改的文件都通过了TypeScript编译检查，没有类型错误或语法错误。

## 后续工作

1. 需要在国际化文件中添加相应的翻译键值
2. 测试所有工具方法的国际化功能
3. 确保前端UI能正确显示国际化后的消息和操作类型

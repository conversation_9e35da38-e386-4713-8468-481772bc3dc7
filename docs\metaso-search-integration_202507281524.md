# Metaso 搜索供应商集成文档

## 概述

本文档记录了 Metaso 搜索供应商的集成过程，包括配置字段定义、组件实现和相关设置。

## 配置字段

Metaso 搜索供应商包含以下配置字段：

1. **base_url** - API 基础地址
   - 默认值：`https://metaso.cn/api/v1/search`
   - 类型：`string`

2. **api_key** - API 密钥
   - 用户填写
   - 类型：`string`

3. **size** - 结果数量
   - 默认值：`10`
   - 类型：`number`

4. **includeSummary** - 是否启用通过网页的摘要信息进行召回增强
   - 默认值：`true`
   - 类型：`boolean`

5. **includeRowContent** - 是否抓取所有来源网页原文
   - 默认值：`false`
   - 类型：`boolean`

## 实现的文件

### 1. 类型定义 (`src/env.d.ts`)

```typescript
export interface MetasoSettings {
  baseUrl: string;
  apiKey: string;
  size: number;
  includeSummary: boolean;
  includeRowContent: boolean;
}

export interface SearchEngineProvider {
  id: string;
  name: string;
  key: 'tavily' | 'metaso' | 'custom';
  config: TavilySettings | MetasoSettings | Record<string, unknown>;
  description?: string;
}
```

### 2. 默认设置 (`src/config/defaultSettings.ts`)

- 添加了 `METASO_APIKEY` 常量
- 添加了 `DEFAULT_METASO_SETTINGS` 配置
- 在 `DEFAULT_SEARCH_ENGINE_SETTINGS` 中添加了 metaso 供应商

### 3. 配置组件 (`src/components/settings/providers/MetasoProviderConfig.vue`)

创建了专门的 Metaso 配置组件，包含：
- Base URL 输入框
- API Key 密码输入框
- Size 数量输入框
- Include Summary 开关
- Include Row Content 开关

### 4. 搜索引擎设置 (`src/components/settings/SearchEngineSettings.vue`)

- 导入了 `MetasoSettings` 类型和 `MetasoProviderConfig` 组件
- 添加了 metaso 配置模板
- 更新了 `updateProviderConfig` 函数类型
- 添加了 metaso 图标获取逻辑

### 5. 资源映射 (`src/config/resourceMap.ts`)

添加了 metaso 的资源映射配置，包括名称、描述和图标路径。

### 6. 图标文件 (`public/icons/llm/metaso-color.svg`)

创建了 Metaso 的 SVG 图标文件。

## 环境变量

在环境配置文件中可以添加：

```bash
METASO_APIKEY=your_metaso_api_key_here
```

## 使用方法

1. 在应用设置中选择 "搜索引擎设置"
2. 在左侧供应商列表中选择 "Metaso"
3. 配置相应的字段：
   - 输入 API Key
   - 调整 Base URL（如需要）
   - 设置结果数量
   - 选择是否启用摘要增强
   - 选择是否抓取原文内容

## 国际化支持

需要在相应的语言文件中添加以下翻译键：

```
src.components.settings.providers.MetasoProviderConfig.baseUrlLabel
src.components.settings.providers.MetasoProviderConfig.baseUrlPlaceholder
src.components.settings.providers.MetasoProviderConfig.baseUrlHint
src.components.settings.providers.MetasoProviderConfig.apiKeyLabel
src.components.settings.providers.MetasoProviderConfig.apiKeyPlaceholder
src.components.settings.providers.MetasoProviderConfig.apiKeyHint
src.components.settings.providers.MetasoProviderConfig.sizeLabel
src.components.settings.providers.MetasoProviderConfig.sizePlaceholder
src.components.settings.providers.MetasoProviderConfig.sizeHint
src.components.settings.providers.MetasoProviderConfig.searchOptions
src.components.settings.providers.MetasoProviderConfig.includeSummaryLabel
src.components.settings.providers.MetasoProviderConfig.includeSummaryHint
src.components.settings.providers.MetasoProviderConfig.includeRowContentLabel
src.components.settings.providers.MetasoProviderConfig.includeRowContentHint
src.config.resourceMap.name.metaso
src.config.resourceMap.description.metaso
```

## 完成状态

✅ 类型定义已添加
✅ 默认设置已配置
✅ 配置组件已创建
✅ 搜索引擎设置已更新
✅ 资源映射已添加
✅ 图标文件已创建
✅ 代码检查通过

## 后续工作

1. 添加国际化翻译
2. 实现 Metaso 搜索工具的具体调用逻辑
3. 添加 Metaso API 的错误处理
4. 编写单元测试
